'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, CandlestickSeries, createSeriesMarkers, SeriesMarker, IPriceLine, Time, LineWidth, LineStyle, SeriesType } from 'lightweight-charts';
import { CandleData, Position, Order, Timeframe, UpdateMode, ChartOrderPlacement, DragCallbacks } from '@/types/trading';

import { DataFeedManager } from '@/utils/dataFeedManager';
import { FibonacciRetracement, FibonacciPoint } from './FibonacciRetracement';
import ChartContextMenu from './ChartContextMenu';
import { useDragState } from '@/hooks/useDragState';
import { isLineDraggable, DEFAULT_DRAG_TOLERANCE, createLineMetadata, validatePendingOrderPrice, validateStopLossTakeProfit, getLineMetadataById } from '@/utils/dragUtils';
import { DragLineType } from '@/types/trading';

interface TradingChartProps {
  data: CandleData[];
  baseData: CandleData[];
  currentIndex: number;
  positions: Position[];
  orders: Order[];
  onPriceClick?: (price: number) => void;
  onChartOrderPlace?: (orderData: ChartOrderPlacement) => void;
  height?: number;
  enableFibonacci?: boolean;
  timeframe?: Timeframe;
  updateMode?: UpdateMode;
  intraCandleStep?: number;
  dataType?: 'candle' | 'tick';
  precision?: number;
  currentBid?: number;
  currentAsk?: number;
  spread?: number;
  // Drag and drop callbacks
  dragCallbacks?: DragCallbacks;
  enableDragAndDrop?: boolean;
}

export default function TradingChart({
  data,
  baseData,
  currentIndex,
  positions,
  orders,
  onPriceClick,
  onChartOrderPlace,
  height = 500,
  enableFibonacci = true,
  timeframe = 'M1',
  updateMode = 'complete',
  intraCandleStep = 0,
  dataType = 'candle',
  precision = 5,
  currentBid,
  currentAsk,
  spread,
  dragCallbacks,
  enableDragAndDrop = false
}: TradingChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const markersRef = useRef<ReturnType<typeof createSeriesMarkers<Time>> | null>(null);
  const currentPriceLineRef = useRef<IPriceLine | null>(null);
  const bidPriceLineRef = useRef<IPriceLine | null>(null);
  const askPriceLineRef = useRef<IPriceLine | null>(null);

  // Drag state management
  const dragState = useDragState({
    callbacks: dragCallbacks,
    tolerance: DEFAULT_DRAG_TOLERANCE
  });

  // Refs for drag state to avoid closure issues in event handlers
  const isDraggingRef = useRef(false);
  const draggedLineIdRef = useRef<string | null>(null);
  const draggedLineTypeRef = useRef<DragLineType | null>(null);
  const dragStartPriceRef = useRef(0);

  // Refs for potential drag state to persist across re-renders
  const dragPotentialRef = useRef(false);
  const dragPotentialLineIdRef = useRef<string | null>(null);
  const dragPotentialStartPriceRef = useRef(0);
  const dragPotentialStartYRef = useRef(0);
  const dragPotentialTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const positionLinesRef = useRef<IPriceLine[]>([]);
  const orderLinesRef = useRef<IPriceLine[]>([]);
  const activePositionLinesRef = useRef<IPriceLine[]>([]);
  const dataFeedRef = useRef<DataFeedManager | null>(null);
  const [isChartReady, setIsChartReady] = useState(false);
  const fibonacciRef = useRef<FibonacciRetracement | null>(null);
  const [isFibonacciActive, setIsFibonacciActive] = useState(false);
  const lastUpdateIndexRef = useRef(-1);
  const lastIntraCandleStepRef = useRef(-1);

  // Context menu state
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuPrice, setContextMenuPrice] = useState(0);

  // Drag validation feedback state
  const [dragValidationError, setDragValidationError] = useState<string | null>(null);
  const [showDragTooltip, setShowDragTooltip] = useState(false);
  const [dragTooltipPosition, setDragTooltipPosition] = useState({ x: 0, y: 0 });
  const [dragPreviewInfo, setDragPreviewInfo] = useState<string | null>(null);

  // Initialize chart and data feed - STABLE: Only recreate when structure changes, not data
  useEffect(() => {
    if (!chartContainerRef.current) return;

    console.log('TradingChart: Initializing chart...');

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      // Configure global price formatting with dynamic precision
      localization: {
        priceFormatter: (price: number) => price.toFixed(precision),
      },
      rightPriceScale: {
        borderColor: '#cccccc',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
        // Configure precision for price scale
        mode: 0, // Normal price scale mode
        autoScale: true,
        invertScale: false,
        alignLabels: true,
        borderVisible: true,
        entireTextOnly: false,
        visible: true,
        ticksVisible: true,
        minimumWidth: 80,
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: timeframe.startsWith('S'), // Show seconds for second-based timeframes
      },
    });

    // Create candlestick series
    const candlestickSeries = chart.addSeries(CandlestickSeries, {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    // Create markers instance
    const markers = createSeriesMarkers(candlestickSeries, []);
    markersRef.current = markers;

    // Create Fibonacci retracement tool
    if (enableFibonacci) {
      const fibonacci = new FibonacciRetracement(candlestickSeries as ISeriesApi<SeriesType>, chart, {
        lineColor: '#2196F3',
        lineWidth: 2,
        showLabels: true,
        showPrices: true,
      });
      candlestickSeries.attachPrimitive(fibonacci);
      fibonacciRef.current = fibonacci;
    }

    // Handle click events
    chart.subscribeClick((param) => {
      if (param.point) {
        const price = candlestickSeries.coordinateToPrice(param.point.y);
        const time = chart.timeScale().coordinateToTime(param.point.x);

        if (price !== null && time !== null) {
          // Handle Fibonacci drawing
          if (isFibonacciActive && fibonacciRef.current && enableFibonacci) {
            const point: FibonacciPoint = { time, price };

            if (fibonacciRef.current.isDrawing()) {
              // Second click: finish drawing but keep the tool active for next drawing
              fibonacciRef.current.updateEndPoint(point);
              fibonacciRef.current.finishDrawing();
              // Don't deactivate the tool, just stop the current drawing
            } else {
              // First click: start new drawing
              fibonacciRef.current.startDrawing(point);
            }
          } else if (onPriceClick) {
            onPriceClick(price);
          }
        }
      }
    });

    // Handle mouse move for Fibonacci preview
    if (enableFibonacci) {
      chart.subscribeCrosshairMove((param) => {
        if (fibonacciRef.current && fibonacciRef.current.isDrawing() && param.point) {
          const price = candlestickSeries.coordinateToPrice(param.point.y);
          const time = chart.timeScale().coordinateToTime(param.point.x);

          if (price !== null && time !== null) {
            fibonacciRef.current.updateEndPoint({ time, price });
          }
        }
      });
    }

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    setIsChartReady(true);

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
      chartRef.current = null;
      candlestickSeriesRef.current = null;
      markersRef.current = null;
      currentPriceLineRef.current = null;
      bidPriceLineRef.current = null;
      askPriceLineRef.current = null;
      positionLinesRef.current = [];
      orderLinesRef.current = [];
      fibonacciRef.current = null;
      // Clear all line metadata
      dragState.clearAllMetadata();
      setIsChartReady(false);
    };
  }, [height, onPriceClick, enableFibonacci, timeframe, updateMode, dataType, precision]);

  // Context menu handler - separate effect to avoid chart recreation
  useEffect(() => {
    if (!chartContainerRef.current || !onChartOrderPlace || !isChartReady || !candlestickSeriesRef.current) return;

    console.log('TradingChart: Setting up context menu handler...', {
      containerExists: !!chartContainerRef.current,
      callbackExists: !!onChartOrderPlace,
      chartReady: isChartReady,
      seriesExists: !!candlestickSeriesRef.current
    });

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();

      console.log('TradingChart: Right-click detected', {
        target: event.target,
        currentTarget: event.currentTarget,
        chartReady: isChartReady,
        seriesExists: !!candlestickSeriesRef.current,
        onChartOrderPlace: !!onChartOrderPlace
      });

      if (!candlestickSeriesRef.current) {
        console.warn('TradingChart: No candlestick series available for context menu');
        return;
      }

      if (!chartContainerRef.current) {
        console.warn('TradingChart: No chart container available for context menu');
        return;
      }

      try {
        const rect = chartContainerRef.current.getBoundingClientRect();
        const y = event.clientY - rect.top;

        console.log('TradingChart: Converting coordinates to price', {
          y,
          clientY: event.clientY,
          rectTop: rect.top,
          rectHeight: rect.height
        });

        // Validate coordinates are within chart bounds
        if (y < 0 || y > rect.height) {
          console.warn('TradingChart: Click outside chart bounds', { y, height: rect.height });
          return;
        }

        // Convert screen coordinates to price
        const price = candlestickSeriesRef.current.coordinateToPrice(y);

        console.log('TradingChart: Converted price:', price);

        if (price !== null && !isNaN(price)) {
          setContextMenuPrice(price);
          setContextMenuPosition({ x: event.clientX, y: event.clientY });
          setContextMenuVisible(true);
          console.log('TradingChart: Context menu should be visible now', {
            price,
            position: { x: event.clientX, y: event.clientY }
          });
        } else {
          console.warn('TradingChart: Invalid price conversion result:', price);
        }
      } catch (error) {
        console.error('TradingChart: Error in context menu handler:', error);
      }
    };

    chartContainerRef.current.addEventListener('contextmenu', handleContextMenu);

    return () => {
      if (chartContainerRef.current) {
        chartContainerRef.current.removeEventListener('contextmenu', handleContextMenu);
      }
    };
  }, [onChartOrderPlace, isChartReady]);

  // Chart-based drag and drop functionality using TradingView events
  useEffect(() => {
    if (!chartRef.current || !enableDragAndDrop || !isChartReady || !candlestickSeriesRef.current) return;

    // REMOVED: Early return that prevented event handler re-registration during drag
    // This was causing the mouse tracking to stop after drag activation
    // if (dragState.dragState.isDragging) {
    //   console.log('Skipping drag system rebuild - drag in progress');
    //   return;
    // }

    const chart = chartRef.current;
    const series = candlestickSeriesRef.current;
    const priceScale = chart.priceScale('right');

    console.log('Setting up chart-based drag system. Tracked lines:', dragState.lineMetadataMap.size);
    console.log('DEBUG: Initial dragState in useEffect:', {
      isDragging: dragState.isDragging,
      draggedLineId: dragState.dragState.draggedLineId,
      dragState: dragState.dragState
    });

    // Refs are now defined at component level

    // Hit test function to find closest line
    const findClosestLineToPoint = (pointY: number): { line: any; metadata: any; distance: number } | null => {
      let closestLine = null;
      let closestMetadata = null;
      let minDistance = Infinity;

      for (const [line, metadata] of dragState.lineMetadataMap.entries()) {
        const lineY = series.priceToCoordinate(metadata.originalPrice);
        if (lineY !== null) {
          const distance = Math.abs(pointY - lineY);
          if (distance < DEFAULT_DRAG_TOLERANCE.lineProximity && distance < minDistance) {
            closestLine = line;
            closestMetadata = metadata;
            minDistance = distance;
          }
        }
      }

      return closestLine && closestMetadata ? { line: closestLine, metadata: closestMetadata, distance: minDistance } : null;
    };

    // Drag detection state is now managed by refs at component level

    // Click handler for drag start/end
    const handleChartClick = (param: any) => {
      if (!param.point) return;

      const pointY = param.point.y;
      const price = series.coordinateToPrice(pointY);

      if (price === null) return;

      // Read current state from refs
      const currentIsDragging = isDraggingRef.current;
      const currentDraggedLineId = draggedLineIdRef.current;

      console.log('CLICK: handleChartClick called', {
        currentIsDragging,
        currentDraggedLineId,
        price
      });

      // Check if we're ending an active drag
      if (currentIsDragging && currentDraggedLineId) {
        console.log('CLICK: Ending drag for line:', currentDraggedLineId, 'at price:', price);

        const success = dragState.dragState.isValidPosition;

        if (success && currentDraggedLineId && dragCallbacks) {
          // Call appropriate callback
          const currentDraggedLineType = dragState.dragState.draggedLineType;
          if (currentDraggedLineType === DragLineType.PENDING_ORDER && dragCallbacks.onPendingOrderPriceUpdate) {
            // Find the actual order ID from metadata
            const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId);
            const orderId = lineResult?.metadata.orderId;

            if (orderId) {
              dragCallbacks.onPendingOrderPriceUpdate(orderId, price).then((callbackSuccess) => {
                if (!callbackSuccess) {
                  // Rollback on failure
                  if (lineResult) {
                    lineResult.line.applyOptions({
                      price: dragStartPriceRef.current,
                      lineWidth: 2 as LineWidth,
                    });
                  }
                }
              });
            }
          } else if (currentDraggedLineType === DragLineType.STOP_LOSS && dragCallbacks.onPositionStopLossUpdate) {
            // Find the actual position ID from metadata
            const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId);
            const positionId = lineResult?.metadata.positionId;

            if (positionId) {
              dragCallbacks.onPositionStopLossUpdate(positionId, price).then((callbackSuccess) => {
                if (!callbackSuccess) {
                  // Rollback on failure
                  if (lineResult) {
                    lineResult.line.applyOptions({
                      price: dragStartPriceRef.current,
                      lineWidth: 2 as LineWidth,
                    });
                  }
                }
              });
            }
          } else if (currentDraggedLineType === DragLineType.TAKE_PROFIT && dragCallbacks.onPositionTakeProfitUpdate) {
            // Find the actual position ID from metadata
            const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId);
            const positionId = lineResult?.metadata.positionId;

            if (positionId) {
              dragCallbacks.onPositionTakeProfitUpdate(positionId, price).then((callbackSuccess) => {
                if (!callbackSuccess) {
                  // Rollback on failure
                  if (lineResult) {
                    lineResult.line.applyOptions({
                      price: dragStartPriceRef.current,
                      lineWidth: 2 as LineWidth,
                    });
                  }
                }
              });
            }
          } else if (currentDraggedLineType === DragLineType.POSITION_ENTRY) {
            // For position entry dragging, create new SL/TP line
            const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId);
            const positionId = lineResult?.metadata.positionId;
            const position = positionId ? positions.find(p => p.id === positionId) : null;

            if (positionId && position) {
              // Determine if this should be a stop loss or take profit
              const isStopLoss = (position.type === 'buy' && price < position.entryPrice) ||
                                (position.type === 'sell' && price > position.entryPrice);

              if (isStopLoss && dragCallbacks.onPositionStopLossUpdate) {
                dragCallbacks.onPositionStopLossUpdate(positionId, price).then((callbackSuccess) => {
                  if (!callbackSuccess) {
                    console.warn('Failed to create stop loss via position entry drag');
                  }
                });
              } else if (!isStopLoss && dragCallbacks.onPositionTakeProfitUpdate) {
                dragCallbacks.onPositionTakeProfitUpdate(positionId, price).then((callbackSuccess) => {
                  if (!callbackSuccess) {
                    console.warn('Failed to create take profit via position entry drag');
                  }
                });
              }
            }
          }
        } else {
          // Rollback if not successful
          const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
          if (lineResult) {
            lineResult.line.applyOptions({
              price: dragState.dragState.startPrice,
              lineWidth: 2 as LineWidth,
            });
          }
        }

        // Reset drag state using refs and hook
        isDraggingRef.current = false;
        draggedLineIdRef.current = null;
        draggedLineTypeRef.current = null;
        dragStartPriceRef.current = 0;

        dragState.endDrag(success);

        // Note: Auto-scaling was never disabled, so no need to re-enable
        // priceScale.applyOptions({ autoScale: true });

        // Note: endDrag already called above

        // Clear validation feedback and preview info
        setDragValidationError(null);
        setShowDragTooltip(false);
        setDragPreviewInfo(null);

        // Reset potential drag state using refs
        dragPotentialRef.current = false;
        dragPotentialLineIdRef.current = null;
        if (dragPotentialTimeoutRef.current) {
          clearTimeout(dragPotentialTimeoutRef.current);
          dragPotentialTimeoutRef.current = null;
        }

        return;
      }

      // Check if we're starting a potential drag
      if (!currentIsDragging && !dragPotentialRef.current) {
        const closestLine = findClosestLineToPoint(pointY);

        console.log('Chart click - closest line:', closestLine ? closestLine.metadata.type : 'none', 'at price:', price);

        if (closestLine && isLineDraggable(closestLine.metadata.type)) {
          console.log('Starting potential drag for line:', closestLine.metadata.id, closestLine.metadata.type);

          // Start potential drag (not active yet) using refs
          dragPotentialRef.current = true;
          dragPotentialLineIdRef.current = closestLine.metadata.id;
          dragPotentialStartPriceRef.current = price;
          dragPotentialStartYRef.current = pointY;

          // Set timeout to cancel potential drag if no movement occurs
          dragPotentialTimeoutRef.current = setTimeout(() => {
            console.log('Canceling potential drag due to timeout');
            dragPotentialRef.current = false;
            dragPotentialLineIdRef.current = null;
            dragPotentialTimeoutRef.current = null;
          }, 2000); // 2 seconds timeout - gives user enough time to start dragging
        }
      }
    };

    // Crosshair move handler for live drag updates and hover feedback
    const handleCrosshairMove = (param: any) => {
      if (!param.point) return;

      const pointY = param.point.y;
      const price = series.coordinateToPrice(pointY);

      if (price === null) return;

      // Read current state from refs for immediate access
      const currentIsDragging = isDraggingRef.current;
      const currentDraggedLineId = draggedLineIdRef.current;
      const currentDraggedLineType = draggedLineTypeRef.current;

      console.log('MOVE: handleCrosshairMove called', {
        currentIsDragging,
        currentDraggedLineId,
        price,
        pointY
      });

      // Check if we have a potential drag that should become active
      if (dragPotentialRef.current && dragPotentialLineIdRef.current && !isDraggingRef.current) {
        // Calculate movement distance to determine if this is a real drag
        const movementDistance = Math.abs(pointY - dragPotentialStartYRef.current);
        const priceMovement = Math.abs(price - dragPotentialStartPriceRef.current);

        // Debug: Log all movements
        console.log('MOVE: Potential drag movement check:', {
          dragPotentialLineId: dragPotentialLineIdRef.current,
          movementDistance,
          priceMovement,
          pointY,
          dragPotentialStartY: dragPotentialStartYRef.current,
          price,
          dragPotentialStartPrice: dragPotentialStartPriceRef.current
        });

        // Activate drag if mouse moved significantly (more than 3 pixels OR some price movement)
        if (movementDistance > 3 || priceMovement > 0.00001) {
          console.log('MOVE: Activating drag for line:', dragPotentialLineIdRef.current, 'movement:', movementDistance, 'price movement:', priceMovement);

          // Activate the drag using refs for immediate state
          const activatingLineResult = getLineMetadataById(dragState.lineMetadataMap, dragPotentialLineIdRef.current);
          if (activatingLineResult) {
            console.log('DEBUG: About to activate drag with refs:', {
              lineId: dragPotentialLineIdRef.current,
              lineType: activatingLineResult.metadata.type,
              startPrice: dragPotentialStartPriceRef.current,
              startY: dragPotentialStartYRef.current
            });

            // Set drag state using refs for immediate effect
            isDraggingRef.current = true;
            draggedLineIdRef.current = dragPotentialLineIdRef.current;
            draggedLineTypeRef.current = activatingLineResult.metadata.type;
            dragStartPriceRef.current = dragPotentialStartPriceRef.current;

            console.log('DEBUG: After setting refs:', {
              isDragging: isDraggingRef.current,
              draggedLineId: draggedLineIdRef.current,
              draggedLineType: draggedLineTypeRef.current
            });

            // Also call the hook for callbacks
            dragState.startDrag(dragPotentialLineIdRef.current, activatingLineResult.metadata.type, dragPotentialStartPriceRef.current, dragPotentialStartYRef.current);

            // Apply visual feedback to the line
            activatingLineResult.line.applyOptions({
              lineWidth: 3 as LineWidth,
            });
          }

          // Reset potential drag state using refs
          dragPotentialRef.current = false;
          dragPotentialLineIdRef.current = null;
          if (dragPotentialTimeoutRef.current) {
            clearTimeout(dragPotentialTimeoutRef.current);
            dragPotentialTimeoutRef.current = null;
          }
        }
      }

      if (currentIsDragging && currentDraggedLineId) {
        // Handle drag updates
        console.log('MOVE: Updating active drag for line:', currentDraggedLineId, 'at price:', price, 'pointY:', pointY);
        let isValidPosition = true;
        let errorMessage = '';

        if (currentDraggedLineType === DragLineType.PENDING_ORDER && currentBid !== undefined && currentAsk !== undefined) {
          const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
          const order = lineResult?.metadata.orderId ? orders.find(o => o.id === lineResult.metadata.orderId) : null;
          if (order) {
            const minDistance = Math.pow(10, -(precision) + 1); // Exactly 1 pip
            const validation = validatePendingOrderPrice(
              order.orderExecutionType,
              price,
              currentBid,
              currentAsk,
              minDistance
            );
            isValidPosition = validation.isValid;
            errorMessage = validation.errorMessage || '';
          }
        } else if (currentDraggedLineType === DragLineType.STOP_LOSS || currentDraggedLineType === DragLineType.TAKE_PROFIT) {
          const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
          const position = lineResult?.metadata.positionId ? positions.find(p => p.id === lineResult.metadata.positionId) : null;
          if (position) {
            const minDistance = Math.pow(10, -(precision) + 1); // Exactly 1 pip
            const validation = validateStopLossTakeProfit(
              currentDraggedLineType,
              position.type,
              price,
              position.entryPrice,
              minDistance
            );
            isValidPosition = validation.isValid;
            errorMessage = validation.errorMessage || '';
          }
        } else if (currentDraggedLineType === DragLineType.POSITION_ENTRY) {
          // For position entry lines, validate as potential SL/TP creation
          const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
          const position = lineResult?.metadata.positionId ? positions.find(p => p.id === lineResult.metadata.positionId) : null;
          if (position) {
            const minDistance = Math.pow(10, -(precision) + 1); // Exactly 1 pip

            // Determine if this would be a stop loss or take profit based on price direction
            const isStopLoss = (position.type === 'buy' && price < position.entryPrice) ||
                              (position.type === 'sell' && price > position.entryPrice);
            const lineType = isStopLoss ? DragLineType.STOP_LOSS : DragLineType.TAKE_PROFIT;

            const validation = validateStopLossTakeProfit(
              lineType,
              position.type,
              price,
              position.entryPrice,
              minDistance
            );
            isValidPosition = validation.isValid;
            errorMessage = validation.errorMessage || '';
          }
        }

        // Update drag state
        dragState.updateDrag(price, pointY, isValidPosition);

        // Update validation feedback with enhanced information
        setDragValidationError(isValidPosition ? null : errorMessage);
        setShowDragTooltip(!isValidPosition);

        // Set preview information for enhanced feedback
        if (currentDraggedLineType === DragLineType.POSITION_ENTRY) {
          const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
          const position = lineResult?.metadata.positionId ? positions.find(p => p.id === lineResult.metadata.positionId) : null;
          if (position) {
            const isStopLoss = (position.type === 'buy' && price < position.entryPrice) ||
                              (position.type === 'sell' && price > position.entryPrice);
            const lineTypeText = isStopLoss ? 'Stop Loss' : 'Take Profit';
            const priceDiff = Math.abs(price - position.entryPrice);
            const pips = (priceDiff * Math.pow(10, precision - 1)).toFixed(1);
            setDragPreviewInfo(`Creating ${lineTypeText} at ${price.toFixed(precision)} (${pips} pips)`);
          }
        } else if (currentDraggedLineType === DragLineType.PENDING_ORDER) {
          setDragPreviewInfo(`Moving order to ${price.toFixed(precision)}`);
        } else if (currentDraggedLineType === DragLineType.STOP_LOSS) {
          setDragPreviewInfo(`Moving Stop Loss to ${price.toFixed(precision)}`);
        } else if (currentDraggedLineType === DragLineType.TAKE_PROFIT) {
          setDragPreviewInfo(`Moving Take Profit to ${price.toFixed(precision)}`);
        }

        // Update line visually
        const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId);
        if (lineResult) {
          let lineColor = '#2196f3';

          if (currentDraggedLineType === DragLineType.PENDING_ORDER) {
            const orderLineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
            const order = orderLineResult?.metadata.orderId ? orders.find(o => o.id === orderLineResult.metadata.orderId) : null;
            if (order && isValidPosition) {
              switch (order.orderExecutionType) {
                case 'buyLimit': lineColor = '#4caf50'; break;
                case 'sellLimit': lineColor = '#f44336'; break;
                case 'buyStop': lineColor = '#26a69a'; break;
                case 'sellStop': lineColor = '#ef5350'; break;
              }
            } else if (!isValidPosition) {
              lineColor = '#ff5722';
            }
          } else if (currentDraggedLineType === DragLineType.STOP_LOSS) {
            lineColor = isValidPosition ? '#f44336' : '#ff5722'; // Red for stop loss
          } else if (currentDraggedLineType === DragLineType.TAKE_PROFIT) {
            lineColor = isValidPosition ? '#4caf50' : '#ff5722'; // Green for take profit
          } else if (currentDraggedLineType === DragLineType.POSITION_ENTRY) {
            // For position entry dragging, show preview color based on SL/TP type
            const lineResult = getLineMetadataById(dragState.lineMetadataMap, currentDraggedLineId!);
            const position = lineResult?.metadata.positionId ? positions.find(p => p.id === lineResult.metadata.positionId) : null;
            if (position) {
              const isStopLoss = (position.type === 'buy' && price < position.entryPrice) ||
                                (position.type === 'sell' && price > position.entryPrice);
              lineColor = isValidPosition ? (isStopLoss ? '#f44336' : '#4caf50') : '#ff5722';
            }
          }

          console.log('MOVE: Updating line visually', {
            lineId: currentDraggedLineId,
            price,
            lineColor,
            isValidPosition
          });

          // Enhanced visual styling during drag
          const lineStyle = !isValidPosition ? LineStyle.Dashed : LineStyle.Solid;
          const lineWidth = isValidPosition ? 3 as LineWidth : 4 as LineWidth;

          lineResult.line.applyOptions({
            price,
            color: lineColor,
            lineWidth,
            lineStyle,
          });

          // Update metadata
          dragState.updateLineMetadata(lineResult.line, {
            originalPrice: price,
            isValid: isValidPosition
          });
        }
      } else if (!dragPotentialRef.current) {
        // Handle hover feedback when not dragging and no potential drag
        const closestLine = findClosestLineToPoint(pointY);

        if (closestLine && isLineDraggable(closestLine.metadata.type)) {
          // Enhanced cursor feedback based on line type
          if (chartContainerRef.current) {
            let cursorStyle = 'grab';
            let title = '';

            switch (closestLine.metadata.type) {
              case DragLineType.PENDING_ORDER:
                title = 'Drag to move pending order';
                break;
              case DragLineType.STOP_LOSS:
                title = 'Drag to move stop loss';
                break;
              case DragLineType.TAKE_PROFIT:
                title = 'Drag to move take profit';
                break;
              case DragLineType.POSITION_ENTRY:
                title = 'Drag to create stop loss or take profit';
                cursorStyle = 'copy'; // Different cursor for creation
                break;
            }

            chartContainerRef.current.style.cursor = cursorStyle;
            chartContainerRef.current.title = title;
          }
        } else {
          // Reset cursor and title
          if (chartContainerRef.current) {
            chartContainerRef.current.style.cursor = '';
            chartContainerRef.current.title = '';
          }
        }
      }
    };

    // Subscribe to chart events
    chart.subscribeClick(handleChartClick);
    chart.subscribeCrosshairMove(handleCrosshairMove);

    return () => {
      // Cleanup
      chart.unsubscribeClick(handleChartClick);
      chart.unsubscribeCrosshairMove(handleCrosshairMove);

      // Note: Auto-scaling was never disabled, so no cleanup needed
      // if (isDragging) {
      //   priceScale.applyOptions({ autoScale: true });
      // }

      // Clear potential drag timeout
      if (dragPotentialTimeoutRef.current) {
        clearTimeout(dragPotentialTimeoutRef.current);
      }

      // Clear validation feedback and preview info
      setDragValidationError(null);
      setShowDragTooltip(false);
      setDragPreviewInfo(null);
    };
  }, [enableDragAndDrop, isChartReady, dragState, orders, currentBid, currentAsk, precision, dragCallbacks]);

  // Initialize/update data feed when data or structure changes (NOT currentIndex)
  useEffect(() => {
    if (!isChartReady || data.length === 0) return;

    // Initialize data feed manager
    const isTickData = dataType === 'tick';
    const dataFeed = new DataFeedManager(data, baseData, timeframe, updateMode, 200, isTickData);
    dataFeed.setCurrentIndex(currentIndex);
    dataFeedRef.current = dataFeed;

    // Set initial data only when data feed is created/recreated
    if (candlestickSeriesRef.current) {
      const visibleChartData = dataFeed.getChartData();
      candlestickSeriesRef.current.setData(visibleChartData);
    }

    // Reset tracking variables when data feed is recreated
    lastUpdateIndexRef.current = -1;
    lastIntraCandleStepRef.current = -1;
  }, [data, baseData, timeframe, updateMode, dataType, isChartReady]);

  // Progressive data update when currentIndex or intraCandleStep changes
  useEffect(() => {
    if (!dataFeedRef.current || !candlestickSeriesRef.current || !isChartReady) return;

    console.log('TradingChart: Progressive data update triggered', {
      currentIndex,
      lastUpdateIndex: lastUpdateIndexRef.current,
      intraCandleStep,
      lastIntraCandleStep: lastIntraCandleStepRef.current,
      dataType,
      updateMode
    });

    const dataFeed = dataFeedRef.current;
    const series = candlestickSeriesRef.current;

    // Update data feed current index and intra-candle step
    dataFeed.setCurrentIndex(currentIndex);

    // Set intra-candle step if in intra-candle mode
    if (updateMode === 'intraCandle' && timeframe !== 'M1') {
      dataFeed.setIntraCandleStep(intraCandleStep);
    }

    // Optimized update logic for tick data to prevent chart state resets
    const isMovingForward = currentIndex > lastUpdateIndexRef.current;
    const isMovingBackward = currentIndex < lastUpdateIndexRef.current;
    const isIntraCandleUpdate = updateMode === 'intraCandle' && intraCandleStep !== lastIntraCandleStepRef.current;
    const isTickData = dataType === 'tick';

    // For tick data, be more conservative with updates to preserve Fibonacci and scaling
    if (isTickData && (isMovingForward || isIntraCandleUpdate)) {
      console.log('TradingChart: Updating tick data (forward/intra-candle)');
      // For tick data: only update if we're moving forward or in intra-candle mode
      // This prevents unnecessary chart resets that destroy Fibonacci lines
      const latestCandle = dataFeed.getLatestCandleForChart();
      if (latestCandle) {
        try {
          // CRITICAL: Use update() instead of setData() to preserve chart state
          // This maintains Fibonacci tools, user scaling, and other chart elements
          console.log('TradingChart: Calling series.update() for tick data', latestCandle);
          series.update(latestCandle);
        } catch (error) {
          console.warn('Chart update failed for tick data:', error);
          // For tick data, avoid fallback to setData as it resets chart state
          // Instead, just log the error and continue
        }
      }
    } else if (!isTickData && (isMovingForward || isIntraCandleUpdate)) {
      console.log('TradingChart: Updating candle data (forward/intra-candle)');
      // For regular candle data: use the existing logic
      const latestCandle = dataFeed.getLatestCandleForChart();
      if (latestCandle) {
        try {
          console.log('TradingChart: Calling series.update() for candle data', latestCandle);
          series.update(latestCandle);

          // Ensure price scale remains visible after update
          if (chartRef.current) {
            chartRef.current.applyOptions({
              rightPriceScale: {
                visible: true,
              },
            });
          }
        } catch (error) {
          console.warn('Chart update failed, falling back to setData:', error);
          try {
            const visibleChartData = dataFeed.getChartData();
            console.log('TradingChart: Calling series.setData() as fallback', visibleChartData.length, 'candles');
            series.setData(visibleChartData);
          } catch (setDataError) {
            console.error('Critical chart error - both update and setData failed:', setDataError);
            // Prevent further chart operations that might cause blanking
            return;
          }
        }
      }
    } else if (isMovingBackward) {
      console.log('TradingChart: Moving backward, using setData()');
      // Going backwards - need to reload visible data
      // Use setData() only when absolutely necessary (going backwards)
      try {
        const visibleChartData = dataFeed.getChartData();
        console.log('TradingChart: Calling series.setData() for backward navigation', visibleChartData.length, 'candles');
        series.setData(visibleChartData);

        // Ensure price scale remains visible after setData
        if (chartRef.current) {
          chartRef.current.applyOptions({
            rightPriceScale: {
              visible: true,
            },
          });
        }
      } catch (error) {
        console.error('Critical chart error during backward navigation:', error);
        // Prevent further chart operations that might cause blanking
        return;
      }
    } else {
      console.log('TradingChart: No chart update needed');
    }

    // Update tracking variables
    lastUpdateIndexRef.current = currentIndex;
    lastIntraCandleStepRef.current = intraCandleStep;
  }, [currentIndex, intraCandleStep, isChartReady, updateMode, timeframe, dataType]);

  // Update chart precision without recreating the chart
  useEffect(() => {
    if (!chartRef.current || !isChartReady) return;

    const chart = chartRef.current;

    // Update localization options to change price formatting precision
    chart.applyOptions({
      localization: {
        priceFormatter: (price: number) => price.toFixed(precision),
      },
    });
  }, [precision, isChartReady]);

  // REMOVED: Update visible range based on current index
  // This was causing auto-zoom reset issues by constantly calling setVisibleRange()
  // The chart should maintain user's zoom level and view position during playback
  // Only auto-scroll when explicitly needed (e.g., real-time mode)

  // Memoized markers to prevent unnecessary recalculations
  const chartMarkers = useMemo(() => {
    const markers: SeriesMarker<Time>[] = [];

    // Add entry markers for ALL orders (both open positions and closed orders)
    // This ensures entry markers remain visible even after closing
    orders.forEach(order => {
      // Instead of using timestamp matching, we need to store the chart index when the order was placed
      // For now, let's add a custom property to track this
      if (order.timestamp && (order as any).chartIndex !== undefined) {
        const chartIndex = (order as any).chartIndex;
        if (chartIndex >= 0 && chartIndex < data.length) {
          const chartTime = Math.floor(data[chartIndex].timestamp / 1000) as Time;

          console.log('Entry marker (using chartIndex):', {
            orderTimestamp: order.timestamp,
            orderDate: new Date(order.timestamp).toISOString(),
            chartIndex,
            chartTime,
            chartDate: new Date(Number(chartTime) * 1000).toISOString(),
            orderType: order.type,
            size: order.size
          });

          markers.push({
            time: chartTime,
            position: order.type === 'buy' ? 'belowBar' : 'aboveBar',
            color: order.type === 'buy' ? '#26a69a' : '#ef5350',
            shape: order.type === 'buy' ? 'arrowUp' : 'arrowDown',
            text: `${order.type.toUpperCase()} ${order.size}`,
            size: 1,
          });
        }
      }
    });

    // Add exit markers for closed orders
    orders
      .filter(order => order.status === 'closed' && order.exitTimestamp)
      .forEach(order => {
        if ((order as any).exitChartIndex !== undefined) {
          const chartIndex = (order as any).exitChartIndex;
          if (chartIndex >= 0 && chartIndex < data.length) {
            const chartTime = Math.floor(data[chartIndex].timestamp / 1000) as Time;
            const pnl = order.pnl || 0;

            console.log('Exit marker (using chartIndex):', {
              orderExitTimestamp: order.exitTimestamp,
              exitDate: new Date(order.exitTimestamp!).toISOString(),
              chartIndex,
              chartTime,
              chartDate: new Date(Number(chartTime) * 1000).toISOString(),
              pnl
            });

            markers.push({
              time: chartTime,
              position: order.type === 'buy' ? 'aboveBar' : 'belowBar',
              color: pnl >= 0 ? '#4caf50' : '#f44336',
              shape: 'circle',
              text: `Close ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)}`,
              size: 1,
            });
          }
        }
      });

    return markers;
  }, [orders, data]);

  // Draw position markers (only when markers actually change)
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !markersRef.current || !isChartReady) return;

    const markersInstance = markersRef.current;

    // Update markers using the new API
    markersInstance.setMarkers(chartMarkers);
  }, [chartMarkers, isChartReady]);

  // Draw position lines (stop loss, take profit) - separate effect to avoid conflicts
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !isChartReady) return;

    // Don't redraw position lines if currently dragging
    if (dragState.dragState.isDragging) {
      console.log('Skipping position lines redraw - drag in progress');
      return;
    }

    const series = candlestickSeriesRef.current;

    // Clear existing position lines and their metadata
    positionLinesRef.current.forEach(line => {
      if (line && series.removePriceLine) {
        // Remove metadata before removing line
        dragState.removeLineMetadata(line);
        series.removePriceLine(line);
      }
    });
    positionLinesRef.current = [];

    // Draw position lines (stop loss, take profit) - only for open positions
    positions.forEach(position => {
      if (position.stopLoss) {
        const stopLossLine = {
          price: position.stopLoss,
          color: '#f44336',
          lineWidth: 2 as LineWidth,
          lineStyle: 2 as LineStyle, // Dashed
          axisLabelVisible: true,
          title: 'SL',
        };
        const line = series.createPriceLine(stopLossLine);
        if (line) {
          positionLinesRef.current.push(line);

          // Add metadata for drag functionality
          const metadata = createLineMetadata(
            `sl-${position.id}`,
            DragLineType.STOP_LOSS,
            position.stopLoss,
            undefined,
            position.id
          );
          dragState.addLineMetadata(line, metadata);
        }
      }

      if (position.takeProfit) {
        const takeProfitLine = {
          price: position.takeProfit,
          color: '#4caf50',
          lineWidth: 2 as LineWidth,
          lineStyle: 2 as LineStyle, // Dashed
          axisLabelVisible: true,
          title: 'TP',
        };
        const line = series.createPriceLine(takeProfitLine);
        if (line) {
          positionLinesRef.current.push(line);

          // Add metadata for drag functionality
          const metadata = createLineMetadata(
            `tp-${position.id}`,
            DragLineType.TAKE_PROFIT,
            position.takeProfit,
            undefined,
            position.id
          );
          dragState.addLineMetadata(line, metadata);
        }
      }
    });
  }, [positions, isChartReady, dragState]);

  // Draw active position entry lines - shows entry price for open positions
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !isChartReady) return;

    // Don't redraw active position lines if currently dragging
    if (dragState.dragState.isDragging) {
      console.log('Skipping active position lines redraw - drag in progress');
      return;
    }

    const series = candlestickSeriesRef.current;

    // Clear existing active position lines and their metadata
    try {
      activePositionLinesRef.current.forEach(line => {
        if (line && series.removePriceLine) {
          // Remove metadata before removing line
          dragState.removeLineMetadata(line);
          series.removePriceLine(line);
        }
      });
    } catch (error) {
      console.warn('Error removing active position lines:', error);
    }
    activePositionLinesRef.current = [];

    // Only create active position lines if there are positions
    if (positions.length === 0) return;

    // Create entry price lines for each active position
    positions.forEach(position => {
      try {
        // Determine color based on position type
        const color = position.type === 'buy' ? '#1976d2' : '#d32f2f'; // Darker blue for buy, darker red for sell

        // Format unrealized P&L for display
        const pnl = position.unrealizedPnL || 0;
        const pnlText = pnl >= 0 ? `+$${pnl.toFixed(2)}` : `$${pnl.toFixed(2)}`;

        // Create title with position info
        const title = `${position.type.toUpperCase()} ${position.size} (${pnlText})`;

        const positionLine = {
          price: position.entryPrice,
          color,
          lineWidth: 2 as LineWidth,
          lineStyle: 0 as LineStyle, // Solid line for active positions
          axisLabelVisible: true,
          title,
        };

        const line = series.createPriceLine(positionLine);
        if (line) {
          activePositionLinesRef.current.push(line);

          // Add metadata for drag functionality (for SL/TP creation)
          const metadata = createLineMetadata(
            `entry-${position.id}`,
            DragLineType.POSITION_ENTRY,
            position.entryPrice,
            undefined,
            position.id
          );
          dragState.addLineMetadata(line, metadata);
        }
      } catch (error) {
        console.warn('Error creating active position line for position:', position.id, error);
      }
    });
  }, [positions, isChartReady, dragState]);

  // Memoized pending orders to prevent unnecessary recalculations
  const pendingOrders = useMemo(() => {
    return orders.filter(order => order.status === 'pending');
  }, [orders]);

  // Draw order lines (pending orders) - optimized to prevent chart conflicts
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !isChartReady) return;

    // Don't redraw order lines if currently dragging
    if (dragState.dragState.isDragging) {
      console.log('Skipping order lines redraw - drag in progress');
      return;
    }

    console.log('TradingChart: Drawing order lines', pendingOrders.length, 'pending orders');

    const series = candlestickSeriesRef.current;

    // Clear existing order lines and their metadata safely
    try {
      orderLinesRef.current.forEach(line => {
        if (line && series.removePriceLine) {
          // Remove metadata before removing line
          dragState.removeLineMetadata(line);
          series.removePriceLine(line);
        }
      });
    } catch (error) {
      console.warn('Error removing order lines:', error);
    }
    orderLinesRef.current = [];

    // Only create order lines if there are pending orders
    if (pendingOrders.length === 0) return;

    // Create order lines with error handling
    pendingOrders.forEach(order => {
      try {
        const executionPrice = order.executionPrice || order.entryPrice;

        // Determine color and style based on order execution type
        let color = '#2196f3'; // Default blue
        let title = '';

        switch (order.orderExecutionType) {
          case 'buyLimit':
            color = '#4caf50'; // Green
            title = `BUY LIMIT ${order.size}`;
            break;
          case 'sellLimit':
            color = '#f44336'; // Red
            title = `SELL LIMIT ${order.size}`;
            break;
          case 'buyStop':
            color = '#26a69a'; // Teal
            title = `BUY STOP ${order.size}`;
            break;
          case 'sellStop':
            color = '#ef5350'; // Light red
            title = `SELL STOP ${order.size}`;
            break;
          default:
            title = `${order.type.toUpperCase()} ${order.size}`;
        }

        const orderLine = {
          price: executionPrice,
          color,
          lineWidth: 2 as LineWidth,
          lineStyle: 3 as LineStyle, // Dotted for pending orders
          axisLabelVisible: true,
          title,
        };

        const line = series.createPriceLine(orderLine);
        if (line) {
          orderLinesRef.current.push(line);

          // Add metadata for drag functionality
          const metadata = createLineMetadata(
            `order-${order.id}`,
            DragLineType.PENDING_ORDER,
            executionPrice,
            order.id,
            undefined
          );
          dragState.addLineMetadata(line, metadata);
        }
      } catch (error) {
        console.warn('Error creating order line for order:', order.id, error);
      }
    });
  }, [pendingOrders, isChartReady, dragState]);

  // Bid/Ask price lines - shows current market bid/ask prices
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !isChartReady) return;

    const series = candlestickSeriesRef.current;

    // Remove existing bid/ask lines
    if (bidPriceLineRef.current && series.removePriceLine) {
      series.removePriceLine(bidPriceLineRef.current);
      bidPriceLineRef.current = null;
    }
    if (askPriceLineRef.current && series.removePriceLine) {
      series.removePriceLine(askPriceLineRef.current);
      askPriceLineRef.current = null;
    }

    // Add bid/ask lines if both prices are available
    if (currentBid !== undefined && currentAsk !== undefined) {
      // Bid line (red)
      const bidLine = {
        price: currentBid,
        color: '#ef5350',
        lineWidth: 2 as LineWidth,
        lineStyle: 1 as LineStyle, // Dotted
        axisLabelVisible: true,
        title: 'BID',
      };
      bidPriceLineRef.current = series.createPriceLine(bidLine);

      // Ask line (green)
      const askLine = {
        price: currentAsk,
        color: '#26a69a',
        lineWidth: 2 as LineWidth,
        lineStyle: 1 as LineStyle, // Dotted
        axisLabelVisible: true,
        title: 'ASK',
      };
      askPriceLineRef.current = series.createPriceLine(askLine);
    }
  }, [currentBid, currentAsk, isChartReady]);

  // Current price line - shows the actual current step price, not the final close
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !isChartReady || !dataFeedRef.current || currentIndex >= data.length) return;

    const series = candlestickSeriesRef.current;
    const dataFeed = dataFeedRef.current;

    // Remove existing current price line
    if (currentPriceLineRef.current && series.removePriceLine) {
      series.removePriceLine(currentPriceLineRef.current);
      currentPriceLineRef.current = null;
    }

    // Get the current step price from the data feed manager
    const latestCandle = dataFeed.getLatestCandleForChart();
    if (latestCandle) {
      const currentPriceLine = {
        price: latestCandle.close, // This is the current step price, not the final close
        color: '#2196f3',
        lineWidth: 2 as LineWidth,
        lineStyle: 0 as LineStyle, // Solid
        axisLabelVisible: true,
        title: '', // Remove the text label to avoid obscuring candlesticks
      };

      // Create new current price line and store reference
      currentPriceLineRef.current = series.createPriceLine(currentPriceLine);
    }
  }, [currentIndex, intraCandleStep, isChartReady, updateMode, timeframe]);

  if (data.length === 0) {
    return (
      <div
        className="flex items-center justify-center trading-panel"
        style={{ height }}
      >
        <div className="text-center">
          <div className="text-trading-text-secondary text-lg mb-2">No chart data available</div>
          <div className="text-trading-text-muted text-sm">Import a CSV file to view the chart</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="trading-panel overflow-hidden">
        {/* Chart header */}
        <div className="px-4 py-3 bg-trading-surface border-b border-trading-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h3 className="text-sm font-semibold text-trading-text-primary">
                Price Chart
              </h3>
              {currentIndex < data.length && (
                <div className="text-xs text-trading-text-secondary">
                  {data[currentIndex]?.date} {data[currentIndex]?.time}
                </div>
              )}
              {enableFibonacci && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setIsFibonacciActive(!isFibonacciActive)}
                    className={`px-3 py-1 text-xs rounded transition-colors ${
                      isFibonacciActive
                        ? 'bg-blue-500 text-white'
                        : 'bg-trading-surface-light text-trading-text-secondary hover:bg-trading-surface-lighter'
                    }`}
                  >
                    Fibonacci
                  </button>
                  {fibonacciRef.current && (
                    <button
                      onClick={() => {
                        fibonacciRef.current?.clear();
                        setIsFibonacciActive(false);
                      }}
                      className="px-3 py-1 text-xs rounded bg-trading-surface-light text-trading-text-secondary hover:bg-trading-surface-lighter transition-colors"
                    >
                      Clear
                    </button>
                  )}
                </div>
              )}
            </div>
            {currentIndex < data.length && dataFeedRef.current && (() => {
              const latestCandle = dataFeedRef.current!.getLatestCandleForChart();
              return latestCandle ? (
                <div className="flex items-center space-x-4 text-xs font-mono">
                  <span className="text-trading-text-secondary">O: <span className="text-trading-text-primary">{latestCandle.open.toFixed(precision)}</span></span>
                  <span className="text-trading-text-secondary">H: <span className="text-trading-text-primary">{latestCandle.high.toFixed(precision)}</span></span>
                  <span className="text-trading-text-secondary">L: <span className="text-trading-text-primary">{latestCandle.low.toFixed(precision)}</span></span>
                  <span className="text-trading-text-secondary">C: <span className="text-trading-text-primary">{latestCandle.close.toFixed(precision)}</span></span>
                  {currentBid !== undefined && currentAsk !== undefined && (
                    <>
                      <span className="text-trading-danger">Bid: <span className="text-trading-text-primary">{currentBid.toFixed(precision)}</span></span>
                      <span className="text-trading-success">Ask: <span className="text-trading-text-primary">{currentAsk.toFixed(precision)}</span></span>
                      {spread !== undefined && (
                        <span className="text-trading-warning">Spread: <span className="text-trading-text-primary">{spread.toFixed(precision + 1)}</span></span>
                      )}
                    </>
                  )}
                </div>
              ) : null;
            })()}
          </div>
        </div>

        {/* Chart container */}
        <div ref={chartContainerRef} style={{ height }} className="bg-trading-surface" />
      </div>

      {/* Context Menu */}
      {onChartOrderPlace && currentBid !== undefined && currentAsk !== undefined && (
        <ChartContextMenu
          isVisible={contextMenuVisible}
          position={contextMenuPosition}
          clickedPrice={contextMenuPrice}
          currentBidAsk={{
            bid: currentBid,
            ask: currentAsk,
            spread: spread || (currentAsk - currentBid),
            timestamp: Date.now()
          }}
          precision={precision}
          onPlaceOrder={(orderData) => {
            onChartOrderPlace(orderData);
            setContextMenuVisible(false);
          }}
          onClose={() => setContextMenuVisible(false)}
        />
      )}

      {/* Drag Validation Error */}
      {showDragTooltip && dragValidationError && (
        <div className="fixed top-4 right-4 z-50 px-4 py-2 text-sm text-white bg-red-600 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <span className="text-red-200">⚠</span>
            <span>{dragValidationError}</span>
          </div>
        </div>
      )}

      {/* Drag Preview Info */}
      {dragPreviewInfo && isDraggingRef.current && (
        <div className="fixed top-4 left-4 z-50 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <span className="text-blue-200">↔</span>
            <span>{dragPreviewInfo}</span>
          </div>
        </div>
      )}
    </div>
  );
}
